# Java开发AI编码助手提示词指南

## 📋 目录
1. [提示词编写核心原则](#提示词编写核心原则)
2. [Java开发场景专用模板](#java开发场景专用模板)
3. [实际案例示例](#实际案例示例)
4. [好坏提示词对比](#好坏提示词对比)
5. [常见问题解决方案](#常见问题解决方案)

---

## 🎯 提示词编写核心原则

### 1. SMART原则
- **S**pecific（具体）：明确指定技术栈、框架版本
- **M**easurable（可衡量）：提供具体的输入输出要求
- **A**chievable（可实现）：设定合理的代码复杂度
- **R**elevant（相关）：聚焦当前开发任务
- **T**ime-bound（有时限）：明确代码风格和约束

### 2. 结构化提示词模板
```
【背景】：项目类型、技术栈、业务场景
【需求】：具体要实现的功能
【约束】：代码规范、性能要求、安全要求
【输入】：现有代码、数据结构、接口定义
【输出】：期望的代码格式、注释要求
【示例】：参考代码或类似实现
```

### 3. 关键要素清单
- ✅ 明确Java版本（Java 8/11/17/21）
- ✅ 指定框架版本（Spring Boot 2.x/3.x）
- ✅ 说明代码风格（Google/阿里巴巴规范）
- ✅ 定义异常处理策略
- ✅ 指定测试覆盖要求
- ✅ 明确性能和安全要求

---

## 🛠️ Java开发场景专用模板

### 1. 类和方法设计

#### 📝 实体类设计模板
```
请基于以下要求设计Java实体类：

【背景】：Spring Boot 3.x项目，使用JPA进行数据持久化
【需求】：设计用户实体类User，包含基础信息和审计字段
【约束】：
- 遵循阿里巴巴Java开发规范
- 使用Lombok减少样板代码
- 实现JPA审计功能
- 添加数据校验注解

【字段要求】：
- id：主键，Long类型，自增
- username：用户名，唯一，非空，3-20字符
- email：邮箱，唯一，非空，格式校验
- password：密码，非空，最少8位
- status：状态，枚举类型（ACTIVE/INACTIVE/LOCKED）
- createdAt/updatedAt：审计字段

【输出要求】：
1. 完整的实体类代码
2. 相关的枚举类
3. 必要的注释说明
```

#### 📝 Service层设计模板
```
请设计Service层业务逻辑：

【背景】：Spring Boot 3.x，分层架构，使用Spring Data JPA
【需求】：实现用户管理服务UserService
【约束】：
- 使用@Transactional管理事务
- 实现完整的CRUD操作
- 添加业务校验逻辑
- 统一异常处理
- 添加日志记录

【方法要求】：
- createUser：创建用户，校验用户名和邮箱唯一性
- getUserById：根据ID查询用户，不存在抛出异常
- updateUser：更新用户信息，校验数据有效性
- deleteUser：软删除用户
- getUserByUsername：根据用户名查询

【输出要求】：
1. Service接口定义
2. Service实现类
3. 自定义异常类
4. 完整的JavaDoc注释
```

### 2. Spring Boot应用开发

#### 📝 Controller设计模板
```
请设计RESTful API Controller：

【背景】：Spring Boot 3.x Web应用，前后端分离架构
【需求】：设计用户管理API接口
【约束】：
- 遵循RESTful设计规范
- 使用统一响应格式
- 添加参数校验
- 实现分页查询
- 添加API文档注解

【接口要求】：
- POST /api/users：创建用户
- GET /api/users/{id}：查询用户详情
- PUT /api/users/{id}：更新用户信息
- DELETE /api/users/{id}：删除用户
- GET /api/users：分页查询用户列表

【技术要求】：
- 使用@Valid进行参数校验
- 使用@ApiOperation添加Swagger文档
- 统一异常处理
- 返回标准化响应格式

【输出要求】：
1. Controller类完整代码
2. DTO类定义
3. 统一响应类
4. 全局异常处理器
```

### 3. 数据库操作

#### 📝 JPA Repository模板
```
请设计JPA数据访问层：

【背景】：Spring Boot 3.x + Spring Data JPA + MySQL 8.0
【需求】：实现用户数据访问层
【约束】：
- 使用Spring Data JPA
- 自定义查询方法
- 支持动态查询
- 添加分页和排序
- 优化查询性能

【查询需求】：
- 根据用户名查询用户
- 根据邮箱查询用户
- 根据状态查询用户列表
- 模糊查询用户名或邮箱
- 按创建时间范围查询
- 统计各状态用户数量

【输出要求】：
1. Repository接口
2. 自定义查询方法
3. @Query注解的复杂查询
4. Specification动态查询示例
5. 性能优化建议
```

#### 📝 MyBatis Mapper模板
```
请设计MyBatis数据访问层：

【背景】：Spring Boot 3.x + MyBatis Plus + MySQL 8.0
【需求】：实现用户数据访问层
【约束】：
- 使用MyBatis Plus
- XML和注解混合使用
- 支持动态SQL
- 实现批量操作
- 添加SQL性能监控

【功能要求】：
- 基础CRUD操作
- 条件查询和分页
- 批量插入和更新
- 复杂关联查询
- 统计查询

【输出要求】：
1. Mapper接口
2. XML映射文件
3. 实体类配置
4. 动态SQL示例
5. 批量操作实现
```

### 4. 单元测试编写

#### 📝 单元测试模板
```
请为以下Java类编写完整的单元测试：

【背景】：Spring Boot 3.x项目，使用JUnit 5 + Mockito + TestContainers
【测试目标】：UserService业务逻辑层
【约束】：
- 测试覆盖率达到90%以上
- 使用@MockBean模拟依赖
- 测试正常流程和异常情况
- 使用TestContainers进行集成测试
- 遵循AAA模式（Arrange-Act-Assert）

【测试场景】：
- 正常创建用户
- 用户名重复异常
- 邮箱格式错误
- 查询不存在用户
- 更新用户信息
- 软删除用户

【输出要求】：
1. 完整的测试类
2. Mock对象配置
3. 测试数据准备
4. 异常测试用例
5. 集成测试示例
```

### 5. 异常处理

#### 📝 异常处理模板
```
请设计完整的异常处理机制：

【背景】：Spring Boot 3.x Web应用，需要统一异常处理
【需求】：设计分层异常处理体系
【约束】：
- 自定义业务异常
- 全局异常处理器
- 统一错误响应格式
- 异常日志记录
- 国际化错误消息

【异常类型】：
- 业务异常（用户不存在、权限不足等）
- 参数校验异常
- 数据库操作异常
- 系统异常
- 第三方服务异常

【输出要求】：
1. 异常类层次结构
2. 全局异常处理器
3. 错误码定义
4. 统一响应格式
5. 异常处理最佳实践
```

### 6. 性能优化

#### 📝 性能优化模板
```
请分析并优化以下Java代码的性能：

【背景】：Spring Boot 3.x高并发Web应用
【问题】：[描述具体的性能问题]
【约束】：
- 保持代码可读性
- 考虑内存使用
- 优化数据库查询
- 合理使用缓存
- 异步处理优化

【优化方向】：
- 算法复杂度优化
- 数据库查询优化
- 缓存策略设计
- 并发处理优化
- JVM参数调优

【输出要求】：
1. 性能问题分析
2. 优化后的代码
3. 性能对比数据
4. 优化策略说明
5. 监控和测试建议
```

### 7. 代码重构

#### 📝 代码重构模板
```
请重构以下Java代码，提高代码质量：

【背景】：[项目背景和技术栈]
【问题】：[当前代码存在的问题]
【约束】：
- 保持功能不变
- 提高代码可读性
- 降低代码复杂度
- 遵循设计原则
- 提高可测试性

【重构目标】：
- 消除代码坏味道
- 应用设计模式
- 提取公共方法
- 优化类结构
- 改善命名规范

【输出要求】：
1. 重构后的代码
2. 重构步骤说明
3. 设计模式应用
4. 代码质量对比
5. 测试用例更新
```

### 8. API设计

#### 📝 API设计模板
```
请设计RESTful API接口：

【背景】：Spring Boot 3.x微服务架构
【需求】：[具体的API功能需求]
【约束】：
- 遵循RESTful规范
- 统一响应格式
- 完整的错误处理
- API版本管理
- 安全认证机制

【设计要求】：
- 资源命名规范
- HTTP方法使用
- 状态码定义
- 请求响应格式
- 分页和过滤
- API文档生成

【输出要求】：
1. API接口定义
2. 请求响应模型
3. 错误码定义
4. Swagger文档配置
5. 安全配置示例
```

---

## 💡 实际案例示例

### 案例1：用户注册功能实现

**提示词：**
```
请实现用户注册功能，要求如下：

【背景】：Spring Boot 3.x + Spring Security + JPA + MySQL
【需求】：实现用户注册API，包含邮箱验证
【约束】：
- 密码BCrypt加密
- 邮箱唯一性校验
- 发送验证邮件
- 注册限流保护
- 完整的单元测试

【技术栈】：
- Spring Boot 3.1.0
- Spring Security 6.x
- Spring Data JPA
- MySQL 8.0
- Redis（缓存验证码）
- JavaMail（邮件发送）

【输出要求】：
1. Controller层API接口
2. Service层业务逻辑
3. Repository层数据访问
4. DTO和实体类
5. 邮件服务实现
6. 单元测试用例
```

**预期效果：**
- 生成完整的用户注册功能代码
- 包含安全性考虑和最佳实践
- 提供可直接运行的代码示例

---

## ⚖️ 好坏提示词对比

### ❌ 差的提示词示例
```
帮我写一个用户类
```

**问题分析：**
- 缺乏具体需求
- 没有技术栈信息
- 没有约束条件
- 无法确定输出格式

### ✅ 好的提示词示例
```
请基于Spring Boot 3.x + JPA设计用户实体类：

【需求】：电商系统用户实体
【字段】：id、username、email、password、phone、status、createdAt、updatedAt
【约束】：
- 使用Lombok注解
- JPA审计功能
- 数据校验注解
- 遵循阿里巴巴规范

【输出】：完整实体类 + 状态枚举 + 注释说明
```

**优势分析：**
- 明确技术栈和版本
- 详细的字段要求
- 具体的约束条件
- 清晰的输出期望

---

## 🔧 常见问题解决方案

### 1. 代码生成不完整
**问题**：AI只生成部分代码
**解决方案**：
- 在提示词中明确要求"完整的代码实现"
- 分步骤请求：先接口定义，再实现类
- 使用"请继续"来获取剩余代码

### 2. 技术栈版本不匹配
**问题**：生成的代码使用了错误的API
**解决方案**：
- 明确指定框架版本号
- 提供当前项目的依赖信息
- 要求使用最新稳定版本

### 3. 代码风格不统一
**问题**：生成的代码风格混乱
**解决方案**：
- 指定代码规范（Google/阿里巴巴）
- 提供现有代码示例作为参考
- 要求遵循项目既定风格

### 4. 缺少异常处理
**问题**：生成的代码没有异常处理
**解决方案**：
- 在约束中明确要求异常处理
- 指定异常处理策略
- 要求添加日志记录

### 5. 测试用例覆盖不全
**问题**：单元测试场景不完整
**解决方案**：
- 明确测试覆盖率要求
- 列出具体测试场景
- 要求包含边界条件测试

---

## 📚 提示词模板库

### 快速模板
```
# 基础CRUD
基于Spring Boot 3.x + JPA实现[实体名]的CRUD操作，包含Controller、Service、Repository三层架构，添加参数校验和异常处理。

# 数据库查询
使用Spring Data JPA实现[查询需求]，要求支持分页、排序和动态条件查询，优化查询性能。

# 单元测试
为[类名]编写JUnit 5单元测试，覆盖所有公共方法，包含正常流程和异常情况，使用Mockito模拟依赖。

# 性能优化
分析并优化[代码片段]的性能问题，考虑算法复杂度、内存使用和并发处理，提供优化建议。

# API设计
设计[功能模块]的RESTful API，遵循REST规范，包含完整的请求响应格式和错误处理机制。
```

---

## 🎯 使用建议

1. **渐进式提示**：从简单需求开始，逐步添加复杂约束
2. **上下文保持**：在同一会话中保持技术栈和规范的一致性
3. **代码审查**：对AI生成的代码进行人工审查和测试
4. **持续优化**：根据实际效果调整提示词模板
5. **团队共享**：建立团队级别的提示词库和最佳实践

---

## 🚀 高级提示词技巧

### 1. 链式提示词策略
```
# 第一步：架构设计
请设计电商订单系统的整体架构，包含订单、商品、用户、支付四个核心模块的类图和关系。

# 第二步：实体设计
基于上述架构，详细设计Order实体类，包含所有必要字段和关联关系。

# 第三步：业务逻辑
实现OrderService的核心业务方法：创建订单、支付订单、取消订单、查询订单。

# 第四步：API接口
设计订单管理的RESTful API，包含完整的CRUD操作和业务接口。
```

### 2. 上下文增强技巧
```
【项目上下文】：
- 项目类型：B2B电商平台
- 用户规模：10万+企业用户
- 并发要求：1000+ QPS
- 数据量级：千万级订单
- 技术栈：Spring Boot 3.x + MySQL + Redis + RabbitMQ

【代码规范】：
- 遵循阿里巴巴Java开发手册
- 使用SonarQube质量检查
- 测试覆盖率要求80%+
- 接口响应时间<200ms

基于以上上下文，请实现[具体需求]...
```

### 3. 角色扮演提示词
```
请以资深Java架构师的身份，为我设计一个高并发的秒杀系统：

【角色要求】：
- 10年+Java开发经验
- 熟悉高并发系统设计
- 精通Spring生态和中间件
- 注重代码质量和性能

【设计要求】：
- 支持10万+并发
- 防止超卖问题
- 保证数据一致性
- 优雅降级机制

请从架构设计、技术选型、核心代码实现三个维度给出完整方案。
```

### 4. 问题诊断提示词
```
我的Spring Boot应用出现以下问题，请帮我诊断和解决：

【问题现象】：[详细描述问题]
【错误日志】：[粘贴完整错误堆栈]
【相关代码】：[提供问题相关的代码片段]
【环境信息】：
- Java版本：
- Spring Boot版本：
- 数据库：
- 中间件：

【期望输出】：
1. 问题根因分析
2. 解决方案步骤
3. 预防措施建议
4. 相关最佳实践
```

---

## 📊 提示词效果评估

### 评估维度
1. **代码质量**：语法正确性、逻辑合理性、规范遵循度
2. **完整性**：功能完整度、异常处理、测试覆盖
3. **可维护性**：代码结构、注释质量、扩展性
4. **性能考虑**：算法效率、资源使用、并发安全
5. **安全性**：输入校验、权限控制、数据保护

### 改进策略
- 根据生成结果调整提示词细节
- 增加具体的约束条件
- 提供更多上下文信息
- 使用分步骤的渐进式提示

---

## 🔗 相关资源

### 官方文档
- [Spring Boot官方文档](https://spring.io/projects/spring-boot)
- [Spring Data JPA参考指南](https://spring.io/projects/spring-data-jpa)
- [阿里巴巴Java开发手册](https://github.com/alibaba/p3c)

### 代码规范工具
- Checkstyle：代码风格检查
- SpotBugs：静态代码分析
- SonarQube：代码质量管理
- PMD：代码缺陷检测

### 测试工具
- JUnit 5：单元测试框架
- Mockito：Mock测试框架
- TestContainers：集成测试
- JMeter：性能测试

---

## 📋 附录：快速参考模板

### A1. 常用实体类模板
```java
// 提示词：请基于以下模板创建[实体名]实体类
@Entity
@Table(name = "table_name")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EntityListeners(AuditingEntityListener.class)
public class EntityName {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // 业务字段
    @Column(nullable = false, unique = true)
    private String businessField;

    // 审计字段
    @CreatedDate
    @Column(updatable = false)
    private LocalDateTime createdAt;

    @LastModifiedDate
    private LocalDateTime updatedAt;

    @CreatedBy
    @Column(updatable = false)
    private String createdBy;

    @LastModifiedBy
    private String updatedBy;
}
```

### A2. 标准Controller模板
```java
// 提示词：请基于以下模板创建[模块名]Controller
@RestController
@RequestMapping("/api/v1/resources")
@Api(tags = "资源管理")
@Validated
@Slf4j
public class ResourceController {

    @Autowired
    private ResourceService resourceService;

    @PostMapping
    @ApiOperation("创建资源")
    public ResponseEntity<ApiResponse<ResourceDTO>> create(
            @Valid @RequestBody CreateResourceRequest request) {
        // 实现逻辑
    }

    @GetMapping("/{id}")
    @ApiOperation("查询资源详情")
    public ResponseEntity<ApiResponse<ResourceDTO>> getById(
            @PathVariable @Min(1) Long id) {
        // 实现逻辑
    }

    @GetMapping
    @ApiOperation("分页查询资源")
    public ResponseEntity<ApiResponse<PageResult<ResourceDTO>>> getPage(
            @Valid ResourceQueryRequest request) {
        // 实现逻辑
    }
}
```

### A3. Service层模板
```java
// 提示词：请基于以下模板创建[模块名]Service
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class ResourceServiceImpl implements ResourceService {

    @Autowired
    private ResourceRepository resourceRepository;

    @Override
    public ResourceDTO create(CreateResourceRequest request) {
        // 1. 参数校验
        // 2. 业务逻辑处理
        // 3. 数据持久化
        // 4. 返回结果
    }

    @Override
    @Transactional(readOnly = true)
    public ResourceDTO getById(Long id) {
        return resourceRepository.findById(id)
            .map(this::convertToDTO)
            .orElseThrow(() -> new ResourceNotFoundException("资源不存在: " + id));
    }

    private ResourceDTO convertToDTO(Resource resource) {
        // 实体转DTO逻辑
    }
}
```

### A4. 异常处理模板
```java
// 提示词：请基于以下模板创建全局异常处理器
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<ApiResponse<Void>> handleBusinessException(BusinessException e) {
        log.warn("业务异常: {}", e.getMessage());
        return ResponseEntity.ok(ApiResponse.error(e.getCode(), e.getMessage()));
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResponse<Void>> handleValidationException(
            MethodArgumentNotValidException e) {
        // 参数校验异常处理
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<Void>> handleException(Exception e) {
        log.error("系统异常", e);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(ApiResponse.error("SYSTEM_ERROR", "系统异常"));
    }
}
```

### A5. 测试类模板
```java
// 提示词：请基于以下模板创建[类名]的单元测试
@ExtendWith(MockitoExtension.class)
class ResourceServiceTest {

    @Mock
    private ResourceRepository resourceRepository;

    @InjectMocks
    private ResourceServiceImpl resourceService;

    @Test
    @DisplayName("创建资源-成功")
    void createResource_Success() {
        // Given
        CreateResourceRequest request = CreateResourceRequest.builder()
            .name("测试资源")
            .build();

        Resource savedResource = Resource.builder()
            .id(1L)
            .name("测试资源")
            .build();

        when(resourceRepository.save(any(Resource.class))).thenReturn(savedResource);

        // When
        ResourceDTO result = resourceService.create(request);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(1L);
        assertThat(result.getName()).isEqualTo("测试资源");

        verify(resourceRepository).save(any(Resource.class));
    }

    @Test
    @DisplayName("查询资源-资源不存在")
    void getById_ResourceNotFound() {
        // Given
        Long resourceId = 1L;
        when(resourceRepository.findById(resourceId)).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> resourceService.getById(resourceId))
            .isInstanceOf(ResourceNotFoundException.class)
            .hasMessage("资源不存在: 1");
    }
}
```

---

## 🎯 提示词优化检查清单

### ✅ 基础要素检查
- [ ] 明确指定Java版本
- [ ] 指定Spring Boot版本
- [ ] 说明项目类型和规模
- [ ] 定义代码规范要求
- [ ] 明确输出格式要求

### ✅ 技术细节检查
- [ ] 指定数据库类型和版本
- [ ] 说明缓存策略（如Redis）
- [ ] 定义消息队列需求
- [ ] 明确安全认证方式
- [ ] 指定日志框架和级别

### ✅ 质量要求检查
- [ ] 异常处理策略
- [ ] 单元测试覆盖率
- [ ] 性能要求指标
- [ ] 代码注释要求
- [ ] 文档生成需求

### ✅ 业务场景检查
- [ ] 并发处理要求
- [ ] 数据一致性需求
- [ ] 事务管理策略
- [ ] 分页和排序需求
- [ ] 国际化支持需求

---

## 💡 实战技巧总结

### 1. 分层提示策略
```
第一层：架构设计 → 整体结构和技术选型
第二层：接口定义 → API设计和数据模型
第三层：核心实现 → 业务逻辑和数据访问
第四层：测试完善 → 单元测试和集成测试
第五层：优化改进 → 性能优化和代码重构
```

### 2. 上下文管理技巧
- 在会话开始时建立项目上下文
- 使用一致的命名规范和代码风格
- 保持技术栈版本的一致性
- 及时更新和补充上下文信息

### 3. 质量控制要点
- 要求添加完整的JavaDoc注释
- 强调异常处理和边界条件
- 要求提供单元测试用例
- 关注代码的可读性和维护性

### 4. 效率提升建议
- 建立团队共享的提示词库
- 定期更新和优化提示词模板
- 收集和分析AI生成代码的质量
- 建立代码审查和改进流程

---

**文档版本**：v1.0
**更新时间**：2025-08-05
**适用范围**：Java 8+ / Spring Boot 2.x+ / 主流AI编码助手
**维护者**：Java开发团队
**反馈渠道**：[团队邮箱或Issue地址]

---

> 💡 **使用提示**：这份指南是一个活文档，建议根据团队的实际需求和AI工具的特点进行定制化调整。定期收集使用反馈，持续优化提示词模板，以获得更好的代码生成效果。
