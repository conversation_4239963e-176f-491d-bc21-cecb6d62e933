# Java开发AI编码助手提示词核心法则

## 法则1：技术栈精确指定法则 ⭐⭐⭐⭐⭐ (最高重要性)

**核心原理：** 技术栈版本直接决定API可用性和代码兼容性，模糊的技术栈信息会导致AI生成过时或不兼容的代码。

**错误示例：**
```
帮我写一个Spring Boot的用户管理功能
```

**正确示例：**
```
请基于以下技术栈实现用户管理功能：
- Java 17
- Spring Boot 3.1.5
- Spring Data JPA 3.1.5
- MySQL 8.0
- Maven 3.9.x
- 遵循阿里巴巴Java开发规范
```

**实际效果对比：**
- 错误做法：可能生成Spring Boot 2.x的配置方式，使用已废弃的API
- 正确做法：生成与项目完全兼容的代码，避免版本冲突和编译错误

---

## 法则2：结构化需求描述法则 ⭐⭐⭐⭐⭐ (最高重要性)

**核心原理：** 结构化的需求描述确保AI理解完整的业务场景，避免生成功能不完整或逻辑错误的代码。

**错误示例：**
```
写一个订单服务类
```

**正确示例：**
```
请实现订单服务类OrderService：

【背景】：电商系统核心业务模块
【功能需求】：
- 创建订单（库存校验、价格计算、优惠券应用）
- 支付订单（调用支付网关、更新订单状态）
- 取消订单（退款处理、库存回滚）
- 查询订单（支持多条件查询和分页）

【业务规则】：
- 订单创建后30分钟内未支付自动取消
- 支持部分退款
- 订单状态：待支付→已支付→已发货→已完成→已取消

【技术约束】：
- 使用@Transactional管理事务
- 添加分布式锁防止并发问题
- 集成Redis缓存提升查询性能
```

**实际效果对比：**
- 错误做法：只生成基础CRUD方法，缺少业务逻辑和异常处理
- 正确做法：生成完整的业务服务类，包含所有业务规则和技术实现

---

## 法则3：强制真实调用约束法则 ⭐⭐⭐⭐⭐ (最高重要性)

**核心原理：** 防止AI生成假装的代码调用或模拟数据，确保每个方法都有真实的实现逻辑。

**错误示例：**
```
实现用户登录功能
```

**正确示例：**
```
实现用户登录功能，要求：

【强制约束】：
- 严禁使用模拟数据或假装的方法调用
- 每个依赖服务都必须有真实的注入和调用
- 所有异常情况都必须有具体的处理逻辑
- 不允许使用TODO注释代替实现

【实现要求】：
- 真实的数据库查询实现
- 完整的密码加密验证逻辑
- 具体的JWT Token生成代码
- 详细的异常处理和日志记录
```

**实际效果对比：**
- 错误做法：生成包含`// TODO: 实现登录逻辑`的伪代码
- 正确做法：生成可直接运行的完整登录实现，包含所有细节

---

## 法则4：分层架构明确法则 ⭐⭐⭐⭐ (高重要性)

**核心原理：** 明确指定代码层次和职责分离，确保生成的代码符合企业级应用的架构规范。

**错误示例：**
```
写一个用户注册的代码
```

**正确示例：**
```
请按照分层架构实现用户注册功能：

【Controller层】：
- 接收HTTP请求和参数校验
- 调用Service层业务逻辑
- 返回统一格式的响应

【Service层】：
- 业务逻辑处理和规则校验
- 事务管理和异常处理
- 调用Repository层数据访问

【Repository层】：
- 数据持久化操作
- 自定义查询方法
- 数据库事务支持

【DTO/VO层】：
- 请求响应数据传输对象
- 数据校验注解
- 实体转换方法

请为每一层生成完整的代码实现。
```

**实际效果对比：**
- 错误做法：生成单一类包含所有逻辑，违反单一职责原则
- 正确做法：生成符合分层架构的多个类，职责清晰，易于维护

---

## 法则5：异常处理强制法则 ⭐⭐⭐⭐ (高重要性)

**核心原理：** 企业级应用必须有完善的异常处理机制，缺少异常处理的代码在生产环境中极其危险。

**错误示例：**
```
实现用户查询方法
```

**正确示例：**
```
实现用户查询方法，必须包含完整的异常处理：

【异常处理要求】：
- 用户不存在时抛出UserNotFoundException
- 数据库连接异常的处理
- 参数校验失败的处理
- 权限不足的异常处理

【日志记录要求】：
- 关键操作记录INFO日志
- 异常情况记录ERROR日志
- 包含用户ID、操作时间等上下文信息

【返回值处理】：
- 统一的响应格式
- 明确的错误码定义
- 用户友好的错误消息
```

**实际效果对比：**
- 错误做法：生成没有异常处理的代码，遇到错误直接崩溃
- 正确做法：生成健壮的代码，能够优雅处理各种异常情况

---

## 法则6：测试用例同步法则 ⭐⭐⭐⭐ (高重要性)

**核心原理：** 高质量的代码必须配备完整的测试用例，测试驱动开发是现代软件开发的基本要求。

**错误示例：**
```
写一个计算订单总价的方法
```

**正确示例：**
```
实现订单总价计算方法，同时提供完整的测试用例：

【功能实现】：
- 计算商品总价
- 应用优惠券折扣
- 计算运费
- 处理税费

【测试用例要求】：
- 正常计算场景测试
- 边界值测试（0元订单、超大金额）
- 异常场景测试（无效优惠券、商品不存在）
- Mock依赖服务的测试
- 测试覆盖率达到90%以上

【输出要求】：
1. 完整的业务方法实现
2. 对应的JUnit 5测试类
3. 测试数据准备方法
4. Mock对象配置
```

**实际效果对比：**
- 错误做法：只生成业务代码，没有测试保障，代码质量无法验证
- 正确做法：生成代码和测试用例，确保功能正确性和代码质量

---

## 法则7：性能考虑前置法则 ⭐⭐⭐ (中等重要性)

**核心原理：** 在代码设计阶段就考虑性能问题，避免后期重构的高昂成本。

**错误示例：**
```
实现用户列表查询功能
```

**正确示例：**
```
实现高性能的用户列表查询功能：

【性能要求】：
- 支持10万+用户数据查询
- 响应时间<200ms
- 支持高并发访问

【性能优化策略】：
- 使用分页查询避免全表扫描
- 添加合适的数据库索引
- 使用Redis缓存热点数据
- 实现查询条件的预编译
- 避免N+1查询问题

【监控要求】：
- 添加方法执行时间监控
- 记录慢查询日志
- 统计缓存命中率
```

**实际效果对比：**
- 错误做法：生成简单的全表查询，在大数据量下性能极差
- 正确做法：生成考虑性能优化的查询实现，能够应对生产环境需求

---

## 法则8：安全规范嵌入法则 ⭐⭐⭐ (中等重要性)

**核心原理：** 安全问题必须在代码编写阶段就考虑，后期修复安全漏洞的成本极高。

**错误示例：**
```
实现用户密码修改功能
```

**正确示例：**
```
实现安全的用户密码修改功能：

【安全要求】：
- 验证用户身份（当前密码校验）
- 新密码强度校验（长度、复杂度）
- 密码BCrypt加密存储
- 防止SQL注入攻击
- 添加操作日志记录

【输入校验】：
- 参数非空校验
- 密码格式正则验证
- 防止XSS攻击
- 请求频率限制

【权限控制】：
- 只能修改自己的密码
- 管理员权限特殊处理
- 会话有效性验证
```

**实际效果对比：**
- 错误做法：生成简单的密码更新代码，存在多种安全漏洞
- 正确做法：生成安全可靠的密码修改实现，符合安全开发规范

---

## 📋 法则应用检查清单

### 使用前检查 ✅
- [ ] 是否明确指定了技术栈版本？
- [ ] 是否使用结构化格式描述需求？
- [ ] 是否要求真实实现而非伪代码？
- [ ] 是否明确了分层架构要求？

### 代码质量检查 ✅
- [ ] 是否要求完整的异常处理？
- [ ] 是否同时要求测试用例？
- [ ] 是否考虑了性能优化？
- [ ] 是否包含安全规范要求？

### 输出验证检查 ✅
- [ ] 生成的代码是否可直接编译？
- [ ] 是否包含完整的依赖注入？
- [ ] 是否有详细的注释说明？
- [ ] 是否符合代码规范要求？

---

## 🎯 法则综合应用示例

**综合应用所有法则的完整提示词：**

```
请基于以下要求实现电商订单管理功能：

【技术栈】(法则1)：
- Java 17 + Spring Boot 3.1.5 + Spring Data JPA + MySQL 8.0
- Redis 7.0 + RabbitMQ 3.12 + Maven 3.9.x

【结构化需求】(法则2)：
【背景】：B2C电商平台核心订单模块
【功能】：订单创建、支付、取消、查询、状态流转
【业务规则】：30分钟支付超时、库存扣减、优惠券应用

【强制约束】(法则3)：
- 严禁TODO注释和伪代码实现
- 所有方法必须有完整的业务逻辑
- 真实的数据库操作和缓存调用

【分层架构】(法则4)：
- Controller：API接口和参数校验
- Service：业务逻辑和事务管理  
- Repository：数据访问和自定义查询
- DTO/Entity：数据传输和实体对象

【异常处理】(法则5)：
- 自定义业务异常类
- 全局异常处理器
- 完整的错误码定义
- 详细的日志记录

【测试要求】(法则6)：
- JUnit 5 + Mockito单元测试
- 覆盖率90%以上
- 包含正常和异常场景
- Mock外部依赖服务

【性能要求】(法则7)：
- 支持1000+QPS并发
- 查询响应时间<200ms
- 使用Redis缓存优化
- 数据库查询优化

【安全要求】(法则8)：
- 参数校验和SQL注入防护
- 用户权限验证
- 敏感操作日志记录
- 数据脱敏处理

请生成完整的四层架构代码实现。
```

---

**法则制定原则**：这8个法则按重要性递减排序，前5个为核心法则（必须遵循），后3个为质量法则（建议遵循）。在实际使用中，建议根据项目复杂度和时间要求灵活应用。
