/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.lubricate.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tenant.mp.TenantEntity;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.simas.common.IOrderField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 设备润滑工单表视图实体类
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
@Data
@Accessors(chain = true)
@TableName("simas_lubricate_order")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "LubricateOrder对象", description = "润滑工单")
public class LubricateOrder extends TenantEntity implements IOrderField {

	private static final long serialVersionUID = 1L;

	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty(value = "计划id")
	private Long planId;

	@JsonSerialize(
		using = ToStringSerializer.class
	)
	@ApiModelProperty(value = "标准id")
	private Long standardsId;

	/**
	 * 工单编号
	 */
	@ApiModelProperty(value = "工单编号")
	private String no;

	/**
	 * 工单名称
	 */
	@ApiModelProperty(value = "工单名称")
	private String name;


	@ApiModelProperty(value = "设备id")
	private Long equipmentId;

	@ApiModelProperty(value = "设备编号")
	private String equipmentCode;

	/**
	 * 上次润滑时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty(value = "上次润滑时间")
	private Date lastTime;

	/**
	 * 计划时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty(value = "计划时间")
	private Date planTime;

	/**
	 * 工单实际执行时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATE)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATE)
	@ApiModelProperty(value = "工单实际执行时间")
	private Date executeTime;

	/**
	 * 浮动时间
	 */
	@ApiModelProperty(value = "浮动时间")
	private Integer floatTime;

	/**
	 * 检查人员
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "检查人员")
	private Long checkUser;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "责任部门")
	private Long chargeDept;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "责任人员")
	private Long chargeUser;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "执行人员")
	private Long executeUser;

	/**
	 * 驳回原因
	 */
	@ApiModelProperty(value = "驳回原因")
	private String rejectReason;

	/**
	 * 标准信息json: {"equipmentId":"","equipmentName":"","equipmentMonitorId":"","equipmentMonitorName":"","lubricateStandardsId":"","oilTypeId":"","oilTypeName":"","lubricateMethodsId":"","lubricateMethodsName":"","lubricateCycle":"","floatTime":"","sort":"","startTime":""}
	 */
	@ApiModelProperty(value = "标准信息json")
	private String standardsInfo;

	@ApiModelProperty(value = "图片id集合")
	private String attachIds;

	/**
	 * 完成时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "完成时间")
	private Date completeTime;

	/**
	 * 工单提交时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "工单提交时间")
	private Date submitTime;

	/**
	 * 未排期
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "未排期")
	private Integer unscheduled;

	@ApiModelProperty(value = "备注")
	private String remark;

	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "备件耗材")
	private String material;
	@ApiModelProperty(value = "是否需要审批 v1.2.1")
	private Boolean isNeedApproval;


	/**
	 * 是否异常
	 */
	@ApiModelProperty(value = "是否异常")
	private Integer isAbnormal;
	/**
	 * 异常等级
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "异常等级")
	private Integer abnormalLevel;
	/**
	 * 异常描述
	 */
	@ApiModelProperty(value = "异常描述")
	private String abnormalComment;
	/**
	 * 是否现场处理
	 */
	@ApiModelProperty(value = "是否现场处理")
	private Integer isHandled;

	@Override
	public Long getDeptId() {
		return this.getChargeDept();
	}


}
