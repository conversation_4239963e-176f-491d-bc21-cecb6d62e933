/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.dashboard.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.simas.dashboard.dto.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 工单大屏服务接口
 * 负责各类工单的状态统计、超期分析和任务覆盖率统计
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
public interface IOrderDashboardService {

	/**
	 * 查询已超期的工单列表（分页）
	 * ServiceImpl层只负责数据查询，不包含业务逻辑判断
	 * 使用数据库层面的联合查询，只查询已超期状态的工单
	 *
	 * @param page 分页参数
	 * @return 超期工单分页列表
	 */
	IPage<OverdueOrderStatisticsDto> getOverdueOrderStatistics(IPage<OverdueOrderStatisticsDto> page);

	/**
	 * 统计点巡检工单状态分布
	 * ServiceImpl层只负责数据查询，不包含业务逻辑判断
	 * 使用数据库层面的聚合查询，按状态分组统计数量
	 *
	 * @param days 统计天数，null表示当日
	 * @return 点巡检工单状态项列表
	 */
	List<OrderStatusStatisticsDto.OrderStatusItem> getInspectOrderStatusStatistics(Integer days);

	/**
	 * 统计保养工单状态分布
	 * ServiceImpl层只负责数据查询，不包含业务逻辑判断
	 * 使用数据库层面的聚合查询，按状态分组统计数量
	 *
	 * @param days 统计天数，null表示当日
	 * @return 保养工单状态项列表
	 */
	List<OrderStatusStatisticsDto.OrderStatusItem> getMaintainOrderStatusStatistics(Integer days);

	/**
	 * 查询近30天维修工单列表（分页）
	 * ServiceImpl层只负责数据查询，不包含业务逻辑判断
	 * 使用数据库层面的查询，查询近30天内的维修工单
	 *
	 * @param page 分页参数
	 * @return 维修工单分页列表
	 */
	IPage<RepairOrderStatisticsDto> getLast30DaysRepairOrderStatistics(IPage<RepairOrderStatisticsDto> page);

	/**
	 * 查询近一年维修耗时统计（按部位分组分页）
	 * ServiceImpl层只负责数据查询，不包含业务逻辑判断
	 * 使用数据库层面的GROUP BY查询，按设备部位分组统计耗时
	 * 数据源：simas_repair_record.duration字段（关联simas_repair表）
	 * 只统计已完成状态的维修工单（status = 2 已完成，status = 4 超期完成）
	 *
	 * @param page 分页参数
	 * @return 维修耗时统计分页列表（按部位分组）
	 */
	IPage<RepairDurationStatisticsDto> getLastYearRepairDurationStatistics(IPage<RepairDurationStatisticsDto> page);

	/**
	 * 计算近一年维修耗时平均值
	 * ServiceImpl层只负责数据查询，不包含业务逻辑判断
	 * 使用数据库层面的聚合查询，计算平均耗时
	 * 数据源：simas_repair_record.duration字段（关联simas_repair表）
	 * 只统计已完成状态的维修工单（status = 2 已完成，status = 4 超期完成）
	 *
	 * @return 平均耗时（小时）
	 */
	BigDecimal getLastYearRepairDurationAverage();

	/**
	 * 查询当日点巡检工单基础数据
	 * ServiceImpl层只负责数据查询，不包含业务逻辑判断
	 * 简化查询逻辑，只获取基础数据，统计计算在业务层进行
	 *
	 * @return 当日点巡检工单基础数据列表
	 */
	List<InspectOrderSimpleDto> getTodayInspectOrderSimpleData();

	/**
	 * 查询近30天保养工单基础数据
	 * ServiceImpl层只负责数据查询，不包含业务逻辑判断
	 * 简化查询逻辑，只获取基础数据，统计计算在业务层进行
	 *
	 * @return 近30天保养工单基础数据列表
	 */
	List<InspectOrderSimpleDto> getLast30DaysMaintainOrderSimpleData();

}
