package com.snszyk.simas.overhaul.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * MCP用户详情查询请求VO
 *
 * <AUTHOR>
 * @date 2025-01-17
 */
@Data
@ApiModel(value = "UserDetailMcpVO对象", description = "MCP用户详情查询请求参数")
public class UserDetailMcpVO {

    @NotBlank(message = "维修人员姓名不能为空")
    @ApiModelProperty(value = "维修人员真实姓名", required = true, example = "张三")
    private String realName;
}
