package com.snszyk.simas.overhaul.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * MCP派单结果DTO
 *
 * <AUTHOR>
 * @date 2025-01-17
 */
@Data
@ApiModel(value = "DispatchResultDTO对象", description = "MCP派单操作结果")
public class DispatchResultDTO {

    @ApiModelProperty(value = "工单号")
    private String orderNo;

    @ApiModelProperty(value = "维修人员ID")
    private Long repairmanId;

    @ApiModelProperty(value = "维修人员姓名")
    private String repairmanName;

    @ApiModelProperty(value = "派单时间")
    private LocalDateTime dispatchTime;

    @ApiModelProperty(value = "派单结果详细信息")
    private String resultMessage;

    /**
     * 构造方法
     */
    public DispatchResultDTO() {
    }

    /**
     * 构造方法
     *
     * @param orderNo 工单号
     * @param repairmanId 维修人员ID
     * @param repairmanName 维修人员姓名
     * @param resultMessage 结果消息
     */
    public DispatchResultDTO(String orderNo, Long repairmanId, String repairmanName, String resultMessage) {
        this.orderNo = orderNo;
        this.repairmanId = repairmanId;
        this.repairmanName = repairmanName;
        this.resultMessage = resultMessage;
        this.dispatchTime = LocalDateTime.now();
    }
}
