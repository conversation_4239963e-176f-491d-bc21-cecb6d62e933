/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.overhaul.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tenant.mp.TenantEntity;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.simas.common.IOrderField;
import com.snszyk.simas.overhaul.enums.RepairBizTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 设备维修单表实体类
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@Data
@Accessors(chain = true)
@TableName("simas_repair")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Repair对象", description = "设备维修单表")
public class Repair extends TenantEntity implements IOrderField {
	private static final long serialVersionUID = 1L;

	/**
	 * 编号
	 */
	@ApiModelProperty(value = "编号")
	private String no;
	/**
	 * 业务类型（INTERNAL：内部维修，EXTERNAL：外委维修）
	 */
	@ApiModelProperty(value = "业务类型（INTERNAL：内部维修，EXTERNAL：外委维修）")
	private String bizType;
	/**
	 * 设备id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备id")
	private Long equipmentId;
	/**
	 * 部位id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "部位id")
	private Long monitorId;
	/**
	 * 部位名称
	 */
	@ApiModelProperty(value = "部位名称")
	private String monitorName;
	/**
	 * 业务来源（字典：repair_source）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "业务来源（字典：repair_source）")
	private Integer source;
	/**
	 * 业务单号
	 */
	@ApiModelProperty(value = "业务单号")
	private String sourceNo;
	/**
	 * 报修类型（字典：repair_type）报修类型为“故障”时填写故障等级，故障名称
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "报修类型（字典：repair_type）报修类型为“故障”时填写故障等级，故障名称")
	private Integer repairType;
	/**
	 * 故障等级
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "故障等级")
	private Integer faultLevel;
	/**
	 * 故障名称
	 */
	@ApiModelProperty(value = "故障名称")
	private String faultName;
	/**
	 * 报修人
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "报修人")
	private Long reportUser;
	/**
	 * 报修部门
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "报修部门")
	private Long reportDept;
	/**
	 * 报修时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "报修时间")
	private Date reportTime;
	/**
	 * 报修人联系方式
	 */
	@ApiModelProperty(value = "报修人联系方式")
	private String tel;
	/**
	 * 派单人
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "派单人")
	private Long dispatchUser;
	/**
	 * 派单时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "派单时间")
	private Date dispatchTime;
	/**
	 * 维修人
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "维修人")
	private Long receiveUser;
	/**
	 * 维修部门
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "维修部门")
	private Long receiveDept;
	/**
	 * 维修人电话
	 */
	@ApiModelProperty(value = "维修人电话")
	private String receiveUserTel;
	/**
	 * 外部承修单位
	 */
	@ApiModelProperty(value = "外部承修单位")
	private String externalOrg;
	/**
	 * 外部承修单位联系人
	 */
	@ApiModelProperty(value = "外部承修单位联系人")
	private String externalContact;
	/**
	 * 外部承修单位联系方式
	 */
	@ApiModelProperty(value = "外部承修单位联系方式")
	private String externalTel;
	/**
	 * 供应商id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "供应商id")
	private Long supplierId;
	/**
	 * 跟进人
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "跟进人")
	private Long followUser;
	/**
	 * 跟进单位
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "跟进单位")
	private Long followDept;
	/**
	 * 跟进人电话
	 */
	@ApiModelProperty(value = "跟进人电话")
	private String followUserTel;
	/**
	 * 预计完成时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "预计完成时间")
	private Date completeTime;
	/**
	 * 备品备件
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "备品备件")
	private String component;
	/**
	 * 上传图片
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "上传图片")
	private String attachId;
	/**
	 * 问题描述
	 */
	@ApiModelProperty(value = "问题描述")
	private String problemComment;
	/**
	 * 备注
	 */
	@ApiModelProperty(value = "备注")
	private String remark;
	/**
	 * 提交时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "提交时间")
	private Date submitTime;
	/**
	 * 实际完成时间
	 */
	@DateTimeFormat(pattern = DateUtil.PATTERN_DATETIME)
	@JsonFormat(timezone = "GMT+8", pattern = DateUtil.PATTERN_DATETIME)
	@ApiModelProperty(value = "实际完成时间")
	private Date actualCompleteTime;
	/**
	 * 是否即将超时（0否1是）
	 */
	@ApiModelProperty(value = "是否即将超时（0否1是）")
	private Integer isExpired;
	/**
	 * 是否需要审批
	 */
	@ApiModelProperty(value = "是否需要审批 v1.2.1")
	private Boolean isNeedApproval;
	/**
	 * 审批人
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "审批人")
	private Long approvalUser;
	/**
	 * 维修建议
	 */
	@ApiModelProperty(value = "维修建议")
	private String repairSuggest;

	@Override
	public Long getExecuteUser() {
		// 内部维修
		if (RepairBizTypeEnum.INTERNAL.getCode().equals(this.bizType)) {
			return this.receiveUser;
		}
		// 外部维修
		if (RepairBizTypeEnum.EXTERNAL.getCode().equals(this.bizType)) {
			return this.followUser;
		}
		return null;
	}


	@Override
	public Long getDeptId() {
		// 内部维修
		if (RepairBizTypeEnum.INTERNAL.getCode().equals(this.bizType)) {
			return this.receiveDept;
		}
		// 外部维修
		if (RepairBizTypeEnum.EXTERNAL.getCode().equals(this.bizType)) {
			return this.followDept;
		}
		return null;
	}

}
