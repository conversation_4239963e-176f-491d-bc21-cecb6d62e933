/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.simas.leaseback.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.simas.receive.dto.DeviceReceiveDetailDto;
import com.snszyk.simas.receive.entity.DeviceReceiveDetail;
import com.snszyk.simas.receive.vo.DeviceReceiveDetailPageVo;

import java.util.List;

/**
 * 设备领用 服务类
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
public interface IDeviceReceiveDetailService extends BaseService<DeviceReceiveDetail> {



	/**
	 * 分页查询
	 */
	IPage<DeviceReceiveDetailDto> pageList(DeviceReceiveDetailPageVo v);

	/**
	 * 详情
	 */
	DeviceReceiveDetailDto detail(Long id);

	/**
	 * <AUTHOR>
	 * @Description
	 * @Date 上午11:21 2025/3/19
	 * @Param [receiveId]
	 * @return java.util.List<com.snszyk.simas.receive.dto.DeviceReceiveDetailDto>
	 **/
	List<DeviceReceiveDetailDto> listByReceiveId(Long receiveId);

	DeviceReceiveDetailDto detailByDeviceId(Long deviceId, Integer status);


}
