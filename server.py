import argparse
import asyncio

from fastmcp import FastMCP

from tools.statistics_tools import statistics_mcp
from tools.list_tools import list_mcp
from tools.repair_dispatch_tools import repair_dispatch_mcp
from tools.spare_parts_consumption_tools import spare_parts_mcp
from tools.lubricate_statistics_tools import lubricate_mcp
from tools.inspect_statistics_tools import inspect_mcp
from tools.maintain_statistics_tools import maintain_mcp
from tools.repair_statistics_tools import repair_mcp

# 创建主应用
app = FastMCP(name="StatisticsMCP")

# 异步加载子模块
async def setup():
    # 加载报表统计子模块
    await app.import_server("statistics", statistics_mcp)
    # 加载维修派单子模块
    await app.import_server("repair_dispatch", repair_dispatch_mcp)

    # 加载列表查询子模块
    await app.import_server("list", list_mcp)
    # 加载备品备件消耗统计子模块
    await app.import_server(spare_parts_mcp)
    # 加载设备润滑统计子模块
    await app.import_server(lubricate_mcp)
    # 加载设备点巡检统计子模块
    await app.import_server(inspect_mcp)
    # 加载设备保养统计子模块
    await app.import_server(maintain_mcp)
    # 加载设备维修统计子模块
    await app.import_server(repair_mcp)
    # 打印输出已注册的工具
    await list_registered_tools(app)

# 列出已注册的工具
async def list_registered_tools(app):
    tools = await app._list_tools()  # 获取 FunctionTool 列表
    print("✅ 已注册的工具列表：")
    for tool in sorted(tools, key=lambda x: x.name):  # 按名称排序输出
        print(f" - {tool.name}: {tool.description}")

# 启动函数
async def run_server(mode, host="0.0.0.0", port=8000):
    if mode == "stdio":
        print("🚀 启动 STDIO 模式")
        await app.run_async(transport="stdio")
    elif mode == "http":
        print(f"🌐 启动 HTTP 模式在 http://{host}:{port}")
        await app.run_http_async(host=host, port=port)
    elif mode == "sse":
        print(f"📡 启动 SSE 模式在 http://{host}:{port}")
        await app.run_sse_async(host=host, port=port, path="/mcp/simas/sse")
    else:
        raise ValueError(f"不支持的模式: {mode}")

def main():
    parser = argparse.ArgumentParser(description='启动 MCP HTTP API 服务')
    parser.add_argument('--mode', choices=['stdio', 'http', 'sse'],
                        default='sse', help='运行模式 (默认: sse)')
    parser.add_argument('--host', default='0.0.0.0',
                        help='绑定主机 (默认: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=8000,
                        help='监听端口 (默认: 8000)')
    args = parser.parse_args()
    # 先加载模块
    asyncio.run(setup())
    # 启动服务
    asyncio.run(run_server(args.mode, args.host, args.port))

if __name__ == "__main__":
    main()
