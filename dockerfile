# 使用基础镜像
FROM crpi-5mfi8ivth0edtwf9.cn-hangzhou.personal.cr.aliyuncs.com/zhy-space/python:3.10

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV PIP_NO_CACHE_DIR=1
ENV PIP_DISABLE_PIP_VERSION_CHECK=1

# 先复制 requirements.txt
COPY requirements.txt /app/

# 配置 pip 并安装依赖项
RUN pip3 config set global.index-url https://mirrors.aliyun.com/pypi/simple && \
    pip3 config set global.trusted-host mirrors.aliyun.com && \
    pip3 install --upgrade pip setuptools wheel && \
    pip3 install --no-cache-dir -r requirements.txt || \
    pip3 install --no-cache-dir -r requirements.txt --no-deps --force-reinstall

# 复制应用程序代码到容器中
COPY . /app

# 运行应用程序
CMD ["python", "server.py"]