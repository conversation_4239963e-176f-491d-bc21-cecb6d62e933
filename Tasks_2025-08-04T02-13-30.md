[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[ ] NAME:# 背景 DESCRIPTION:1. `doc`目录下的组合提示词是当前系统的提示词，主要用于用户派单和智能问答功能 2. 需要梳理`tools`目录下工具数据系统的功能架构 3. 系统现有派单流程需要优化，新增两个关键工具来改善用户体验  # 需求 在现有派单流程基础上，集成两个新增工具来优化用户交互体验： 1. **查询待派单维修工单工具** - 让用户能够查看和选择待派发的维修工单 2. **查询可用维修人员工具** - 让用户能够查看和选择可用的维修人员  你需要在保持现有流程逻辑的前提下，优化提示词以实现更合理的派单流程。  # 具体场景示例 1. **工单选择场景**：当用户不清楚有哪些待派单工单时，系统应主动调用"待派单内部维修工单"MCP工具，展示可选工单列表供用户选择 2. **人员选择场景**：当用户不确定可用维修人员时，系统应调用"可用维修人员"MCP工具，展示人员列表供用户自主选择  # 技术要求 1. **保持现有逻辑完整性**：    - 工单校验逻辑必须保留    - 维修人员校验逻辑必须保留      - 派单确认逻辑必须保留 2. **新增工具集成**：仅添加两个新工具，不修改现有核心逻辑 3. **用户体验优化**：改善用户交互流程，减少用户输入负担  # 期望结果 1. **逻辑清晰性**：整体派单流程逻辑清晰，现有业务逻辑保持不变 2. **流程流畅性**：用户使用过程更加流畅，减少因信息不足导致的交互中断 3. **向后兼容性**：新优化的提示词必须与现有系统完全兼容  # 输出要求 请提供优化后的完整提示词文件，并说明： - 新增工具的集成点 - 用户交互流程的改进点 - 与现有逻辑的兼容性保证